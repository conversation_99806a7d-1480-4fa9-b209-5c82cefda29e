export default {
    title: 'Компрессор изображений',
    description:
        'Бесплатный онлайн-компрессор изображений для более быстрых веб-сайтов! Уменьшите размер файла ваших изображений WEBP, JPEG и PNG с помощью реализации сжатия на основе Webassembly.',
    tip: 'Не загружено, сжатие в браузере с помощью Webassembly',
    online: 'Сжать изображения онлайн',
    onlineCont1:
        'Бесплатный онлайн-компрессор изображений для более быстрых веб-сайтов! Уменьшите размер файла ваших изображений WEBP, JPEG и PNG с помощью реализации сжатия на основе Webassembly. Пакетное сжатие изображений, поддерживает форматы JPG/JPEG/PNG/WEBP/GIF/SVG/AVIF.',
    onlineCont2:
        'это чисто локальное сжатие без какой-либо серверной логики, оно полностью безопасно.',
    why: 'Зачем вам может понадобиться сжимать изображения?',
    whyCont1:
        'В зависимости от источника изображения, файл может быть довольно большим. Например, JPG с профессиональной зеркальной камеры может иметь десятки мегабайт. В зависимости от ваших потребностей, это может быть слишком большим. Сжатие такого изображения было бы очень полезно.',
    whyCont2:
        'Кроме того, у вас могут быть большие изображения на вашем телефоне. Эти изображения могут занимать много места на жестком диске и препятствовать съемке новых фотографий. Сжатие их может освободить больше внутреннего хранилища, решив эту проблему.',
    vsTitle: 'ShotEasy Компрессор vs TinyPNG Компрессор',
    vsTitle1: 'ShotEasy Компрессор',
    vsTitle2: 'TinyPNG Компрессор',
    vsCont1: 'Использование нативных библиотек C++: libimagequant / libpng / zlib, скомпилированных в WebAssembly, используемых в веб-приложениях.',
    vsCont2: 'Сжатие на уровне памяти с помощью нативного кодирования.',
    vsCont3: 'После загрузки ресурсов, чисто локальное использование в браузере, без необходимости загрузки на сервер',
    vsCont4: 'Бесплатно, без ограничения размера, без ограничения количества, поддержка многоуровневых папок',
    vsCont5: 'Чисто локальный и открытый исходный код, обеспечивающий конфиденциальность и безопасность.',
    vsCont6: 'Веб-сайт TinyPNG достигает сжатия изображений с помощью использования интеллектуальных алгоритмов сжатия. Эти алгоритмы, основанные на технологиях глубокого обучения и машинного обучения, могут обнаруживать и удалять ненужные детали и избыточную информацию в изображениях, тем самым уменьшая размер файлов изображений.',
    vsCont7: 'Алгоритм сжатия TinyPNG в основном обнаруживает неважные пиксели в изображениях и заменяет их более простыми представлениями, тем самым уменьшая размер файла.',
    vsCont8: 'TinyPNG поддерживает загрузку нескольких изображений одновременно, обрабатывая до 20 изображений одновременно.',
    vsCont9: 'Поддерживает только форматы изображений PNG, JPEG, WebP, не поддерживает GIF, SVG и другие форматы',
    vsCont10: 'Максимальная поддержка 5 МБ на изображение, могут быть ограничения для изображений сверхвысокого разрешения',
    vsCont11: 'Риск для конфиденциальности, пользователям необходимо загружать изображения на серверы TinyPNG для сжатия.'
};
