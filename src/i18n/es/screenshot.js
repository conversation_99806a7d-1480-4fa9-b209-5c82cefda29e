export default {
    title: 'Hacer una captura de pantalla en línea',
    description: 'Tomar capturas de pantalla y editar capturas de pantalla en línea',
    tip: 'No se carga y se edita en el navegador',
    how: '¿Cómo hacer una captura de pantalla en línea?',
    howCont1: 'Presione el botón Captura de pantalla.',
    howCont2: 'Seleccione la ventana/pestaña del navegador de la que desea tomar una captura de pantalla.',
    howCont3: 'Haga clic en el botón Cuadrícula para mostrar la imagen de captura de pantalla con fondo.',
    howCont4: 'Haga clic en el botón Recortar para recortar la imagen de captura de pantalla.',
    howCont5: 'Haga clic en el botón Aplicar para aplicar la imagen de captura de pantalla recortada.',
    howCont6: 'Presione el botón Guardar para guardar la imagen de captura de pantalla.',
    howCont7: 'Presione el botón Copiar para copiar la imagen de captura de pantalla.',
    why: '¿Por qué necesita usar capturas de pantalla en línea?',
    whyCont1: 'Acceso directo para usar, no se requieren permisos',
    whyCont2: 'No limitado a ningún sistema, se puede usar en cualquier escenario',
    whyCont3: 'No se requieren plugins',
    can: '¿Puedo usar la herramienta de captura de pantalla proporcionada por Windows/MacOS?',
    canWin: 'Captura de pantalla en PC con Windows',
    canWin1: 'Haga clic en la ventana que desea capturar.',
    canWin2: 'Presione la tecla <span>PrtScn</span>.',
    canWin3: 'Haga clic de nuevo en esta página de Captura de pantalla y presione las teclas <span>Ctrl</span> + <span>V</span>.',
    canMac: 'Captura de pantalla en MacOS',
    canMac1: 'Haga clic en la ventana que desea capturar.',
    canMacScreen: 'Captura de pantalla completa',
    canMacScreen1: 'Presione las teclas <span>shift</span> + <span>⌘command</span> + <span>3</span>.',
    canMacWin: 'Captura de ventana',
    canMacWin1: 'Presione las teclas <span>shift</span> + <span>⌘command</span> + <span>4</span> + <span>space</span>.',
    canMacCrop: 'Captura con recorte',
    canMacCrop1: 'Presione las teclas <span>shift</span> + <span>⌘command</span> + <span>4</span>.'
}