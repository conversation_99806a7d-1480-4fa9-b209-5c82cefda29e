export default {
    title: 'Compresor de Imágenes',
    description: '¡Compresor de imágenes en línea gratuito para sitios web más rápidos! Reduce el tamaño de archivo de tus imágenes WEBP, JPEG y PNG con una implementación de compresión basada en Webassembly.',
    tip: 'No subido, comprimir en el navegador con Webassembly',
    online: 'Comprimir Imágenes en Línea',
    onlineCont1: '¡Compresor de imágenes en línea gratuito para sitios web más rápidos! Reduce el tamaño de archivo de tus imágenes WEBP, JPEG y PNG con una implementación de compresión basada en Webassembly. Compresión por lotes de imágenes, admite formatos JPG/JPEG/PNG/WEBP/GIF/SVG/AVIF.',
    onlineCont2: 'es una compresión puramente local sin ninguna lógica del lado del servidor, es completamente segura.',
    why: '¿Por qué querrías comprimir imágenes?',
    whyCont1: 'Dependiendo de la fuente de una imagen, el archivo podría ser bastante grande. Una JPG de una cámara DSLR profesional, por ejemplo, podría tener docenas de megabytes. Dependiendo de tus necesidades, esto podría ser demasiado grande. Comprimir esta imagen sería muy útil.',
    whyCont2: 'Asimismo, podrías tener imágenes grandes en tu teléfono. Estas imágenes podrían estar ocupando mucho espacio en el disco duro y evitando que tomes más fotos. Comprimirlas podría liberar más almacenamiento interno, solucionando este problema.',
    vsTitle: 'ShotEasy Compress vs TinyPNG Compress',
    vsTitle1: 'ShotEasy Compress',
    vsTitle2: 'TinyPNG Compress',
    vsCont1: 'Uso de bibliotecas nativas C++: libimagequant / libpng / zlib, compiladas en WebAssembly, utilizadas en la web.',
    vsCont2: 'Compresión a nivel de memoria mediante codificación nativa.',
    vsCont3: 'Después de cargar los recursos, uso local puro en el navegador, sin necesidad de subir archivos',
    vsCont4: 'Gratis, sin límite de tamaño, sin límite de número, soporta carpetas multinivel',
    vsCont5: 'Puramente local y de código abierto, garantizando privacidad y seguridad.',
    vsCont6: 'El sitio web TinyPNG logra la compresión de imágenes mediante el uso de algoritmos de compresión inteligentes. Estos algoritmos, basados en tecnologías de aprendizaje profundo y aprendizaje automático, pueden detectar y eliminar detalles innecesarios e información redundante en las imágenes, reduciendo así el tamaño de los archivos de imagen.',
    vsCont7: 'El algoritmo de compresión de TinyPNG principalmente detecta píxeles no importantes en las imágenes y los reemplaza con representaciones más simples, reduciendo así el tamaño del archivo.',
    vsCont8: 'TinyPNG permite subir múltiples imágenes a la vez, procesando hasta 20 imágenes simultáneamente.',
    vsCont9: 'Solo soporta formatos de imagen como PNG, JPEG, WebP, no soporta GIF, SVG y otros formatos',
    vsCont10: 'Soporte máximo de 5MB por imagen, puede tener limitaciones para imágenes de ultra alta resolución',
    vsCont11: 'Riesgo de privacidad, los usuarios necesitan subir las imágenes a los servidores de TinyPNG para la compresión.'
}