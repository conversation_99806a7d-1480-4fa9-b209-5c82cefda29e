export default {
    title: 'オンラインで画像を圧縮する',
    description: 'ウェブサイトの高速化に役立つ無料のオンライン画像圧縮ツール!WebAssemblyベースの圧縮実装により、WEBP、JPEG、およびPNG画像のファイルサイズを縮小できます。',
    tip: 'アップロードされていません。WebAssemblyでブラウザ内で圧縮します',
    online: 'オンラインで画像を圧縮する',
    onlineCont1: 'ウェブサイトの高速化に役立つ無料のオンライン画像圧縮ツール!WebAssemblyベースの圧縮実装により、WEBP、JPEG、およびPNG画像のファイルサイズを縮小できます。JPG/JPEG/PNG/WEBP/GIF/SVG/AVIFフォーマットに対応した一括画像圧縮が可能です。',
    onlineCont2: 'これはサーバーサイドロジックを一切使用しない完全にローカルでの圧縮なので、完全に安全です。',
    why: '画像を圧縮する理由は?',
    whyCont1: '画像のソースによっては、ファイルが非常に大きくなる可能性があります。例えば、プロのデジタル一眼レフカメラから出力されたJPG画像は数十メガバイトにもなります。用途次第では、それが大きすぎる可能性もあります。そのような画像を圧縮すると非常に便利です。',
    whyCont2: '同様に、携帯電話に大きな画像があるかもしれません。これらの画像はハードディスクの多くの容量を占有し、さらに写真を撮れなくなる可能性があります。これらを圧縮すれば、内部ストレージの容量を確保でき、この問題を解決できます。',
    vsTitle: 'ShotEasy 画像圧縮 vs TinyPNG 画像圧縮',
    vsTitle1: 'ShotEasy 画像圧縮',
    vsTitle2: 'TinyPNG 画像圧縮',
    vsCont1: 'ネイティブC++ライブラリを使用：libimagequant / libpng / zlib、WebAssemblyにコンパイルされ、ウェブ上で使用。',
    vsCont2: 'ネイティブエンコーディングによるメモリレベルでの圧縮。',
    vsCont3: 'リソース読み込み後、純粋にブラウザのローカルで使用、アップロードは不要',
    vsCont4: '無料、サイズ制限なし、数の制限なし、多層フォルダをサポート',
    vsCont5: '純粋にローカルでオープンソース、プライバシーとセキュリティを保証。',
    vsCont6: 'TinyPNGウェブサイトは、スマート圧縮アルゴリズムを使用して画像圧縮を実現しています。これらのアルゴリズムは、ディープラーニングと機械学習技術に基づいており、画像内の不必要な詳細や冗長な情報を検出して削除し、画像ファイルサイズを縮小します。',
    vsCont7: 'TinyPNGの圧縮アルゴリズムは、主に画像内の重要でないピクセルを検出し、それらをより単純な表現に置き換えることでファイルサイズを縮小します。',
    vsCont8: 'TinyPNGは一度に複数の画像をアップロードでき、最大20枚の画像を同時に処理できます。',
    vsCont9: 'サポートしている画像形式はPNG、JPEG、WebPのみで、GIF、SVGなどの他の形式はサポートしていません',
    vsCont10: '1枚の画像につき最大5MBまでサポート、超高解像度の画像に対しては制限がある可能性があります',
    vsCont11: 'プライバシーリスク、ユーザーは圧縮のためにTinyPNGのサーバーに画像をアップロードする必要があります。'
}