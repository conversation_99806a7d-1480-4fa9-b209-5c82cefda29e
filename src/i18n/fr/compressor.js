export default {
    title: "Compresseur d'images",
    description:
        "Compresseur d'images en ligne gratuit pour des sites web plus rapides ! Rédu<PERSON>z la taille des fichiers de vos images WEBP, JPEG et PNG avec une implémentation de compression basée sur Webassembly.",
    tip: 'Pas téléchargé, compressez dans le navigateur avec Webassembly',
    online: 'Compresser les images en ligne',
    onlineCont1:
        "Compresseur d'images en ligne gratuit pour des sites web plus rapides ! Réduisez la taille des fichiers de vos images WEBP, JPEG et PNG avec une implémentation de compression basée sur Webassembly. Compression d'images par lots, prend en charge les formats JPG/JPEG/PNG/WEBP/GIF/SVG/AVIF.",
    onlineCont2:
        "c'est une compression purement locale sans aucune logique côté serveur, c'est complètement sûr.",
    why: 'Pourquoi voudriez-vous compresser des images ?',
    whyCont1:
        "Selon la source d'une image, le fichier pourrait être assez volumineux. Une image JPG d'un appareil photo reflex numérique professionnel, par exemple, pourrait faire des dizaines de mégaoctets. Selon vos besoins, cela pourrait être trop gros. Compresser cette image serait très utile.",
    whyCont2:
        "De même, vous pourriez avoir de grandes images sur votre téléphone. Ces images pourraient prendre beaucoup d'espace sur le disque dur et vous empêcher de prendre d'autres photos. Les compresser pourrait libérer plus d'espace de stockage interne, résolvant ce problème.",
    vsTitle: 'ShotEasy Compress vs TinyPNG Compress',
    vsTitle1: 'ShotEasy Compress',
    vsTitle2: 'TinyPNG Compress',
    vsCont1: 'Utilisation de bibliothèques C++ natives : libimagequant / libpng / zlib, compilées en WebAssembly, utilisées sur le web.',
    vsCont2: 'Compression au niveau de la mémoire par encodage natif.',
    vsCont3: 'Après le chargement des ressources, utilisation purement locale dans le navigateur, sans besoin de téléchargement',
    vsCont4: 'Gratuit, sans limite de taille, sans limite de nombre, supporte les dossiers multi-niveaux',
    vsCont5: 'Purement local et open-source, garantissant la confidentialité et la sécurité.',
    vsCont6: 'Le site web TinyPNG réalise la compression d\'images grâce à l\'utilisation d\'algorithmes de compression intelligents. Ces algorithmes, basés sur des technologies d\'apprentissage profond et d\'apprentissage automatique, peuvent détecter et supprimer les détails inutiles et les informations redondantes dans les images, réduisant ainsi la taille des fichiers image.',
    vsCont7: 'L\'algorithme de compression de TinyPNG détecte principalement les pixels non importants dans les images et les remplace par des représentations plus simples, réduisant ainsi la taille du fichier.',
    vsCont8: 'TinyPNG prend en charge le téléchargement de plusieurs images à la fois, traitant jusqu\'à 20 images simultanément.',
    vsCont9: 'Ne prend en charge que les formats d\'image PNG, JPEG, WebP, ne supporte pas GIF, SVG et autres formats',
    vsCont10: 'Prise en charge maximale de 5 Mo par image, peut avoir des limitations pour les images à très haute résolution',
    vsCont11: 'Risque de confidentialité, les utilisateurs doivent télécharger les images sur les serveurs de TinyPNG pour la compression.'
};
