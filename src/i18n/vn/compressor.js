export default {
    title: 'Nén Ảnh Trực Tuyến',
    description: 'Trình nén ảnh trực tuyến miễn phí cho website nhanh hơn! Giảm kích thước tệp của hình ảnh WEBP, JPEG và PNG của bạn với việc triển khai nén dựa trên Webassembly.',
    tip: 'Không tải lên, nén trong trình duyệt với Webassembly',
    online: 'Nén Ảnh Trực Tuyến',
    onlineCont1: 'Trình nén ảnh trực tuyến miễn phí cho website nhanh hơn! Giảm kích thước tệp của hình ảnh WEBP, JPEG và PNG của bạn với việc triển khai nén dựa trên Webassembly. Nén ảnh hàng loạt, hỗ trợ định dạng JPG/JPEG/PNG/WEBP/GIF/SVG/AVIF.',
    onlineCont2: 'đây là nén hoàn toàn cục bộ mà không có bất kỳ logic máy chủ nào, hoàn toàn an toàn.',
    why: 'Tại sao bạn muốn nén ảnh?',
    whyCont1: 'Tùy thuộc vào nguồn của một hình ảnh, tệp có thể khá lớn. Ví dụ, một JPG từ một máy ảnh DSLR chuyên nghiệp có thể lên đến hàng chục megabyte. Tùy thuộc vào nhu cầu của bạn, điều này có thể quá lớn. Nén hình ảnh này sẽ rất hữu ích.',
    whyCont2: 'Tương tự, bạn có thể có hình ảnh lớn trên điện thoại của mình. Những hình ảnh này có thể chiếm nhiều dung lượng ổ đĩa cứng và ngăn bạn chụp thêm ảnh. Nén chúng có thể giải phóng thêm bộ nhớ trong, giải quyết vấn đề này.',
    vsTitle: 'ShotEasy Nén Ảnh vs TinyPNG Nén Ảnh',
    vsTitle1: 'ShotEasy Nén Ảnh',
    vsTitle2: 'TinyPNG Nén Ảnh',
    vsCont1: 'Sử dụng thư viện C++ gốc: libimagequant / libpng / zlib, được biên dịch thành WebAssembly, sử dụng trên web.',
    vsCont2: 'Nén ở cấp độ bộ nhớ thông qua mã hóa gốc.',
    vsCont3: 'Sau khi tải tài nguyên, sử dụng hoàn toàn cục bộ trên trình duyệt, không cần tải lên',
    vsCont4: 'Miễn phí, không giới hạn kích thước, không giới hạn số lượng, hỗ trợ thư mục nhiều cấp',
    vsCont5: 'Hoàn toàn cục bộ và mã nguồn mở, đảm bảo quyền riêng tư và bảo mật.',
    vsCont6: 'Trang web TinyPNG thực hiện nén ảnh bằng cách sử dụng các thuật toán nén thông minh. Các thuật toán này, dựa trên công nghệ học sâu và học máy, có thể phát hiện và loại bỏ các chi tiết không cần thiết và thông tin dư thừa trong hình ảnh, từ đó giảm kích thước tệp hình ảnh.',
    vsCont7: 'Thuật toán nén của TinyPNG chủ yếu phát hiện các pixel không quan trọng trong hình ảnh và thay thế chúng bằng các biểu diễn đơn giản hơn, từ đó giảm kích thước tệp.',
    vsCont8: 'TinyPNG hỗ trợ tải lên nhiều hình ảnh cùng một lúc, xử lý tối đa 20 hình ảnh đồng thời.',
    vsCont9: 'Chỉ hỗ trợ các định dạng hình ảnh bao gồm PNG, JPEG, WebP, không hỗ trợ GIF, SVG và các định dạng khác',
    vsCont10: 'Hỗ trợ tối đa 5MB cho mỗi hình ảnh, có thể có giới hạn đối với hình ảnh có độ phân giải cực cao',
    vsCont11: 'Rủi ro về quyền riêng tư, người dùng cần tải hình ảnh lên máy chủ của TinyPNG để nén.'
}