export default {
    title: '在线压缩图片',
    description: '免费在线图片压缩,图片格式转换!基于 Webassembly 的压缩实现,可减小您的 WEBP、JPEG 和 PNG 图片的文件大小。',
    tip: '不会上传,使用 Webassembly 在浏览器中压缩',
    online: '在线压缩图片',
    onlineCont1: '免费在线图片压缩器,为更快的网站!基于 Webassembly 的压缩实现,可减小您的 WEBP、JPEG 和 PNG 图片的文件大小。批量图片压缩,支持 JPG/JPEG/PNG/WEBP/GIF/SVG/AVIF 格式。',
    onlineCont2: '这是纯粹的本地压缩,不涉及任何服务器端逻辑,完全安全。',
    why: '为什么要压缩图片?',
    whyCont1: '根据图片的来源,文件可能会非常大。例如,来自专业数码单反相机的 JPG 图片,可能会有几十兆字节。根据您的需求,这可能太大了。压缩这样的图片将非常有用。',
    whyCont2: '同样,您的手机上可能有大图片。这些图片可能占用了大量硬盘空间,阻止您拍摄更多照片。压缩它们可以释放更多内部存储空间,解决这个问题。',
    vsTitle: 'ShotEasy图片压缩 vs TinyPNG图片压缩',
    vsTitle1: 'ShotEasy图片压缩',
    vsTitle2: 'TinyPNG图片压缩',
    vsCont1: '使用原生的C++库: libimagequant / libpng / zlib，编译成WebAssembly，在网页上使用。',
    vsCont2: '通过原生编码，在内存层级压缩。',
    vsCont3: '资源加载后，纯浏览器本地使用，无需上传',
    vsCont4: '免费，不限制大小，不限制数字，支持多层级文件夹',
    vsCont5: '纯本地，和开源，保证隐私安全。',
    vsCont6: 'TinyPNG网站通过使用智能压缩算法来实现对图片的压缩。这个算法基于深度学习和机器学习技术，它能够检测并去除图片中的不必要的细节和冗余信息，从而达到减小图片文件大小的目的。',
    vsCont7: 'TinyPNG压缩算法主要是检测图片中的不重要的像素，并将它们替换为更简单的表示，从而减小文件的大小。',
    vsCont8: 'TinyPNG支持一次性上传多张图片,最多可同时处理20张。',
    vsCont9: '只支持的图片格式包括PNG、JPEG、WebP,不支持GIF、SVG等其他格式',
    vsCont10: '单张图片最大支持5MB,对于超高分辨率的图片可能会有限制',
    vsCont11: '隐私风险,用户需要将图片上传到TinyPNG的服务器进行压缩。'
}