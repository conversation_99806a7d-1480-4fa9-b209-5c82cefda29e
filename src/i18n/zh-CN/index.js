import nav from './nav';
import editor from './editor';
import beautifier from './beautifier';
import rounded from './rounded';
import remover from './remover';
import compressor from './compressor';
import screenshot from './screenshot';

export default {
    title: '在线截图和编辑',
    description:
        '免费在线截图和编辑照片，直接在浏览器中截图和调整图像大小并为任何照片添加滤镜。将图像转换为各种格式，如 jpg、png、jpeg 或 webp。截取特定区域或滚动截取整个页面。',
    keywords:
        'ShotEasy,截图,长页面,编辑照片,照片转换器,图像转换器,在线编辑器,在线更改图像格式,将图像转换为jpg,jpg转webp,jpg转png',
    privacy: '隐私',
    terms: '条款',
    blog: '博客',
    nav,
    editor,
    beautifier,
    rounded,
    remover,
    compressor,
    screenshot
};
