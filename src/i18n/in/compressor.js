export default {
    title: 'Image Compressor',
    description: 'Free online image compressor for faster websites! Reduce the file size of your WEBP, JPEG, and PNG images with compression implementation based on Webassembly.',
    tip: 'Not uploaded, compress in browser with Webassembly',
    online: 'Compress Images Online',
    onlineCont1: 'Free online image compressor for faster websites! Reduce the file size of your WEBP, JPEG, and PNG images with compression implementation based on Webassembly. Batch image compression supports JPG/JPEG/PNG/WEBP/GIF/SVG/AVIF formats.',
    onlineCont2: 'It is purely local compression without any server-side logic, it is completely safe.',
    why: 'Why would you want to compress images?',
    whyCont1: 'Depending on the source of an image, the file could be quite large. A JPG from a professional DSLR camera, for example, could be dozens of megabytes. Depending on your needs, this could be too big. Compressing this image would be very useful.',
    whyCont2: 'Likewise, you might have large images on your phone. These images could be taking up a lot of hard drive space and preventing you from taking more photos. Compressing them could free up more internal storage, fixing this problem.',
    vsTitle: 'ShotEasy Compress vs TinyPNG Compress',
    vsTitle1: 'ShotEasy Compress',
    vsTitle2: 'TinyPNG Compress',
    vsCont1: 'Using native C++ libraries: libimagequant / libpng / zlib, compiled into WebAssembly, used on the web.',
    vsCont2: 'Compression at the memory level through native encoding.',
    vsCont3: 'After resource loading, pure browser-local usage, no upload required',
    vsCont4: 'Free, no size limit, no number limit, supports multi-level folders',
    vsCont5: 'Purely local and open-source, ensuring privacy and security.',
    vsCont6: 'The TinyPNG website achieves image compression through the use of smart compression algorithms. These algorithms, based on deep learning and machine learning technologies, can detect and remove unnecessary details and redundant information in images, thereby reducing image file sizes.',
    vsCont7: 'TinyPNG\'s compression algorithm mainly detects unimportant pixels in images and replaces them with simpler representations, thus reducing file size.',
    vsCont8: 'TinyPNG supports uploading multiple images at once, processing up to 20 images simultaneously.',
    vsCont9: 'Only supports image formats including PNG, JPEG, WebP, does not support GIF, SVG, and other formats',
    vsCont10: 'Maximum support for 5MB per image, may have limitations for ultra-high resolution images',
    vsCont11: 'Privacy risk, users need to upload images to TinyPNG\'s servers for compression.'
}
