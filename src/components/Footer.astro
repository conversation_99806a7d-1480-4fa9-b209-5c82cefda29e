---
import { getRelativeLocaleUrl } from 'astro:i18n';
import {getLocale, getLang} from '@i18n/index';
import { CONFIG, LANGUAGES } from '@lib/config';
const { locale } = Astro.params;
const lang = getLocale(locale);
const l = getLang(locale);
const languages = LANGUAGES as {[key: string]: string}
const reg = new RegExp(`/${lang}`, 'i');
const langUrls = CONFIG.locals.map(item => {
    const href = new URL((lang === 'en' && item !== 'en' ? item : '') + Astro.url.pathname.replace(reg, item === 'en' ? '': item), Astro.url.origin).toString();
    return {href, hrefLang: languages[item] || item, isActive: item.toLocaleLowerCase() === lang.toLocaleLowerCase()};
});
---

<div class="py-8 bg-white">
    <div class="container grid grid-cols-3 items-center gap-2 text-xs text-gray-500">
        <div class="col-span-3 flex justify-start gap-3 text-xs">
            {langUrls.map(item => (
                item.isActive ? <span class="opacity-80 cursor-default">{item.hrefLang}</span> : <a class="hover:text-blue-500 underline" href={item.href} title={item.hrefLang}>{item.hrefLang}</a>
            ))}
        </div>
        <p>Copyright © 2025 ShotEasy</p>
        <div class="flex gap-3 justify-center">
            <a href={getRelativeLocaleUrl(lang, '/privacy-policy')} class="hover:text-blue-500 underline">{l.privacy}</a>
            <a href={getRelativeLocaleUrl(lang, '/terms-of-service')} class="hover:text-blue-500 underline">{l.terms}</a>
            <a href="/blog" class="hover:text-blue-500 underline">{l.blog}</a>
        </div>
        <div class="flex gap-3 justify-end">
            <a href="https://github.com/CH563/shot-easy-website" target="_blank" aria-label="GitHub" class="hover:text-blue-500">
                <svg viewBox="64 64 896 896" focusable="false" data-icon="github" width="16" height="16" fill="currentColor" aria-hidden="true"><path d="M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0138.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z"></path></svg>
            </a>
            <a href="https://twitter.com/LiWen563" target="_blank" aria-label="twitter:@LiWen563" class="hover:text-blue-500">
                <svg fill="currentColor" viewBox="0 0 24 24" width="16" height="16" aria-hidden="true"><g><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path></g></svg>
            </a>
        </div>
    </div>
</div>