---
interface Props {
	width?: number;
	height?: number;
}
const { width, height } = Astro.props;
---

<svg width={width} height={height} viewBox="0 0 510 510" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_i_40_60)">
    <path d="M60.1774 83.172C64.1232 61.4358 84.9426 47.0139 106.679 50.9598L271.777 80.9308C293.513 84.8767 307.935 105.696 303.989 127.432L247.462 438.817C243.516 460.553 222.696 474.975 200.96 471.029L35.8623 441.058C14.1261 437.112 -0.295702 416.293 3.65017 394.557L60.1774 83.172Z" fill="#812BED"/>
    <path d="M179.201 136.846C173.053 115.627 185.269 93.4418 206.488 87.2932L367.654 40.5909C388.872 34.4423 411.058 46.6588 417.206 67.8773L505.29 371.846C511.438 393.065 499.222 415.25 478.003 421.399L316.837 468.101C295.619 474.25 273.433 462.033 267.285 440.815L179.201 136.846Z" fill="#0DB7FF"/>
    <path d="M206.488 87.2932C185.269 93.4418 173.053 115.627 179.201 136.846L254.874 397.986L303.989 127.432C307.935 105.696 293.513 84.8767 271.777 80.9308L245.134 76.0943L206.488 87.2932Z" fill="#EC83FD"/>
    <path d="M401 404C401 416.703 390.703 427 378 427C365.297 427 355 416.703 355 404C355 391.297 365.297 381 378 381C390.703 381 401 391.297 401 404Z" fill="#0D97D3"/>
    <path d="M144 404C144 416.703 133.703 427 121 427C108.297 427 98 416.703 98 404C98 391.297 108.297 381 121 381C133.703 381 144 391.297 144 404Z" fill="#6616CB"/>
    </g>
    <defs>
    <filter id="filter0_i_40_60" x="3" y="39" width="503.881" height="432.679" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dx="-2"/>
    <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
    <feBlend mode="normal" in2="shape" result="effect1_innerShadow_40_60"/>
    </filter>
    </defs>
</svg>