---
import { getRelativeLocaleUrl } from 'astro:i18n';
import Logo from '@components/Logo.astro';
import {getLocale, getLang} from '@i18n/index';
const { pathname } = Astro.url;
const { locale } = Astro.params;
const lang = getLocale(locale);
const l = getLang(locale);
const nav = l.nav;
const index = lang === 'en' ? '/' : `/${lang}`;
const navList = [
    {
        pathname: index,
        title: nav.editor.title,
        name: nav.editor.name
    },
    {
        pathname: getRelativeLocaleUrl(lang, '/screenshot-beautifier'),
        title: nav.beautifier.title,
        name: nav.beautifier.name
    },
    {
        pathname: getRelativeLocaleUrl(lang, '/photo-to-rounded'),
        title: nav.rounded.title,
        name: nav.rounded.name
    },
    {
        pathname: getRelativeLocaleUrl(lang, '/background-remover'),
        title: nav.remover.title,
        name: nav.remover.name
    },
    {
        pathname: getRelativeLocaleUrl(lang, '/image-compressor'),
        title: nav.compressor.title,
        name: nav.compressor.name
    },
    {
        pathname: getRelativeLocaleUrl(lang, '/take-a-screenshot'),
        title: nav.screenshot.title,
        name: nav.screenshot.name
    },
];
---

<header class="container flex flex-col gap-3 items-center justify-center p-4 relative z-10 select-none md:flex-row md:justify-between">
    <a href={index} class="flex items-center gap-2 font-extrabold text-lg text-gray-700"><Logo height={42} />ShotEasy</a>
    <nav class="flex gap-2 text-sm text-slate-600 bg-white rounded-3xl p-1 px-2 [&_span]:rounded-xl [&_span]:px-2 [&_span]:bg-blue-600 [&_span]:text-white [&_a]:rounded-xl [&_a]:px-2 hover:[&_a]:bg-sky-50 hover:[&_a]:text-blue-600">
        {navList.map(item => {
            if (pathname.toLocaleLowerCase() === item.pathname.toLocaleLowerCase()) return (<span>{item.name}</span>)
            return (<a href={item.pathname} title={item.title}>{item.name}</a>)
        })}
    </nav>
</header>