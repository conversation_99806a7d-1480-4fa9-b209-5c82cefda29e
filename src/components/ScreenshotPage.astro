---
import Privacy from '@components/Privacy.astro';
import { getLang } from '@i18n/index';
import Screenshot from '@components/screenshot/Screenshot';
const { locale } = Astro.params;
const l = getLang(locale);
const nav = l.nav;
const t = l.screenshot;
---

<div class="container py-8 relative z-10 flex-1">
    <h1 class="text-4xl text-center leading-[42px] font-bold mb-0.5 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">{t.title}</h1>
    <p class="text-slate-600 text-center mb-2">{t.tip}</p>
    <div class="pb-12 min-h-[320px]">
        <Screenshot client:only="react" />
    </div>
    <section class="mb-3 rounded-md p-4 bg-sky-100/40 text-xs [&>p]:mb-1">
        <div class="mb-1 text-sm">🏙️</div>
        <h2 class="text-lg font-bold mb-2">{t.how}</h2>
        <ol class="list-decimal list-inside [&_li]:py-1">
            <li>{t.howCont1}</li>
            <li>{t.howCont2}</li>
            <li>{t.howCont3}</li>
            <li>{t.howCont4}</li>
            <li>{t.howCont5}</li>
            <li>{t.howCont6}</li>
            <li>{t.howCont7}</li>
        </ol>
    </section>
    <section class="mb-3 rounded-md p-4 bg-pink-100/40 text-xs [&>p]:mb-1">
        <div class="mb-1 text-sm">🤩</div>
        <h2 class="text-lg font-bold mb-2">{t.why}</h2>
        <p>{t.whyCont1}</p>
        <p>{t.whyCont2}</p>
        <p>{t.whyCont3}</p>
    </section>
    <section class="mb-3 rounded-md p-4 bg-green-100/40 text-xs [&>p]:mb-1 [&_span]:inline-block [&_span]:rounded-sm [&_span]:bg-black/70 [&_span]:px-1.5 [&_span]:text-white">
        <div class="mb-1 text-sm">📸</div>
        <h2 class="text-lg font-bold mb-2">{t.can}</h2>
        <h3 class="font-semibold mb-1 text-sm">{t.canWin}</h3>
        <p>{t.canWin1}</p>
        <p set:html={t.canWin2} />
        <p set:html={t.canWin3} />
        <h3 class="font-semibold mb-1 text-sm pt-4">{t.canMac}</h3>
        <p>{t.canMac1}</p>
        <h4 class="font-semibold mb-1 pt-2">1.{t.canMacScreen}</h4>
        <p set:html={t.canMacScreen1} />
        <h4 class="font-semibold mb-1 pt-2">2.{t.canMacWin}</h4>
        <p set:html={t.canMacWin1} />
        <h4 class="font-semibold mb-1 pt-2">3.{t.canMacCrop}</h4>
        <p set:html={t.canMacCrop1} />
    </section>
    <Privacy />
</div>
<script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t.title,
    "description": t.description,
    "operatingSystem": "ALL",
    "applicationCategory": "BrowserApplication",
    "browserRequirements": "requires HTML5 support",
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "3.6",
        "ratingCount": "1448",
        "bestRating": "5"
    },
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
    }
})}/>