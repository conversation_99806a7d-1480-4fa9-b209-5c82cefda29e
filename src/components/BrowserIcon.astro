---
interface Props {
	type?: string;
	width?: number;
	height?: number;
}
const { type, width, height } = Astro.props;
---

{type ==='Firefox' && <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} version="1.1" viewBox="0 0 77.42 79.97">
	<title>Firefox Browser logo</title>
	 <defs>
	  <linearGradient id="a" x1="70.79" x2="6.447" y1="12.39" y2="74.47" gradientTransform="translate(-1.3 -.004086)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" offset=".048"/>
	   <stop stop-color="#ffe847" offset=".111"/>
	   <stop stop-color="#ffc830" offset=".225"/>
	   <stop stop-color="#ff980e" offset=".368"/>
	   <stop stop-color="#ff8b16" offset=".401"/>
	   <stop stop-color="#ff672a" offset=".462"/>
	   <stop stop-color="#ff3647" offset=".534"/>
	   <stop stop-color="#e31587" offset=".705"/>
	  </linearGradient>
	  <radialGradient id="b" cx="-7907" cy="-8515" r="80.8" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#ffbd4f" offset=".129"/>
	   <stop stop-color="#ffac31" offset=".186"/>
	   <stop stop-color="#ff9d17" offset=".247"/>
	   <stop stop-color="#ff980e" offset=".283"/>
	   <stop stop-color="#ff563b" offset=".403"/>
	   <stop stop-color="#ff3750" offset=".467"/>
	   <stop stop-color="#f5156c" offset=".71"/>
	   <stop stop-color="#eb0878" offset=".782"/>
	   <stop stop-color="#e50080" offset=".86"/>
	  </radialGradient>
	  <radialGradient id="c" cx="-7937" cy="-8482" r="80.8" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#960e18" offset=".3"/>
	   <stop stop-color="#b11927" stop-opacity=".74" offset=".351"/>
	   <stop stop-color="#db293d" stop-opacity=".343" offset=".435"/>
	   <stop stop-color="#f5334b" stop-opacity=".094" offset=".497"/>
	   <stop stop-color="#ff3750" stop-opacity="0" offset=".53"/>
	  </radialGradient>
	  <radialGradient id="d" cx="-7927" cy="-8533" r="58.53" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" offset=".132"/>
	   <stop stop-color="#ffdc3e" offset=".252"/>
	   <stop stop-color="#ff9d12" offset=".506"/>
	   <stop stop-color="#ff980e" offset=".526"/>
	  </radialGradient>
	  <radialGradient id="e" cx="-7946" cy="-8461" r="38.47" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#3a8ee6" offset=".353"/>
	   <stop stop-color="#5c79f0" offset=".472"/>
	   <stop stop-color="#9059ff" offset=".669"/>
	   <stop stop-color="#c139e6" offset="1"/>
	  </radialGradient>
	  <radialGradient id="f" cx="-7936" cy="-8492" r="20.4" gradientTransform="matrix(.972 -.235 .275 1.138 10090 7834)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#9059ff" stop-opacity="0" offset=".206"/>
	   <stop stop-color="#8c4ff3" stop-opacity=".064" offset=".278"/>
	   <stop stop-color="#7716a8" stop-opacity=".45" offset=".747"/>
	   <stop stop-color="#6e008b" stop-opacity=".6" offset=".975"/>
	  </radialGradient>
	  <radialGradient id="g" cx="-7938" cy="-8518" r="27.68" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#ffe226" offset="0"/>
	   <stop stop-color="#ffdb27" offset=".121"/>
	   <stop stop-color="#ffc82a" offset=".295"/>
	   <stop stop-color="#ffa930" offset=".502"/>
	   <stop stop-color="#ff7e37" offset=".732"/>
	   <stop stop-color="#ff7139" offset=".792"/>
	  </radialGradient>
	  <radialGradient id="h" cx="-7916" cy="-8536" r="118.1" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" offset=".113"/>
	   <stop stop-color="#ff980e" offset=".456"/>
	   <stop stop-color="#ff5634" offset=".622"/>
	   <stop stop-color="#ff3647" offset=".716"/>
	   <stop stop-color="#e31587" offset=".904"/>
	  </radialGradient>
	  <radialGradient id="i" cx="-7927" cy="-8523" r="86.5" gradientTransform="matrix(.105 .995 -.653 .069 -4685 8470)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" offset="0"/>
	   <stop stop-color="#ffe847" offset=".06"/>
	   <stop stop-color="#ffc830" offset=".168"/>
	   <stop stop-color="#ff980e" offset=".304"/>
	   <stop stop-color="#ff8b16" offset=".356"/>
	   <stop stop-color="#ff672a" offset=".455"/>
	   <stop stop-color="#ff3647" offset=".57"/>
	   <stop stop-color="#e31587" offset=".737"/>
	  </radialGradient>
	  <radialGradient id="j" cx="-7938" cy="-8508" r="73.72" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" offset=".137"/>
	   <stop stop-color="#ff980e" offset=".48"/>
	   <stop stop-color="#ff5634" offset=".592"/>
	   <stop stop-color="#ff3647" offset=".655"/>
	   <stop stop-color="#e31587" offset=".904"/>
	  </radialGradient>
	  <radialGradient id="k" cx="-7919" cy="-8504" r="80.69" gradientTransform="translate(7974 8524)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" offset=".094"/>
	   <stop stop-color="#ffe141" offset=".231"/>
	   <stop stop-color="#ffaf1e" offset=".509"/>
	   <stop stop-color="#ff980e" offset=".626"/>
	  </radialGradient>
	  <linearGradient id="l" x1="70.01" x2="15.27" y1="12.06" y2="66.81" gradientTransform="translate(-1.3 -.004086)" gradientUnits="userSpaceOnUse">
	   <stop stop-color="#fff44f" stop-opacity=".8" offset=".167"/>
	   <stop stop-color="#fff44f" stop-opacity=".634" offset=".266"/>
	   <stop stop-color="#fff44f" stop-opacity=".217" offset=".489"/>
	   <stop stop-color="#fff44f" stop-opacity="0" offset=".6"/>
	  </linearGradient>
	 </defs>
	 <path d="m74.62 26.83c-1.684-4.052-5.1-8.427-7.775-9.81a40.27 40.27 0 0 1 3.925 11.76l7e-3 0.065c-4.382-10.92-11.81-15.33-17.88-24.92-0.307-0.485-0.614-0.971-0.913-1.484-0.171-0.293-0.308-0.557-0.427-0.8a7.053 7.053 0 0 1-0.578-1.535 0.1 0.1 0 0 0-0.088-0.1 0.138 0.138 0 0 0-0.073 0c-5e-3 0-0.013 9e-3 -0.019 0.011s-0.019 0.011-0.028 0.015l0.015-0.026c-9.735 5.7-13.04 16.25-13.34 21.53a19.39 19.39 0 0 0-10.67 4.111 11.59 11.59 0 0 0-1-0.758 17.97 17.97 0 0 1-0.109-9.473 28.7 28.7 0 0 0-9.329 7.21h-0.018c-1.536-1.947-1.428-8.367-1.34-9.708a6.928 6.928 0 0 0-1.294 0.687 28.22 28.22 0 0 0-3.788 3.245 33.84 33.84 0 0 0-3.623 4.347v6e-3 -7e-3a32.73 32.73 0 0 0-5.2 11.74l-0.052 0.256c-0.073 0.341-0.336 2.049-0.381 2.42 0 0.029-6e-3 0.056-9e-3 0.085a36.94 36.94 0 0 0-0.629 5.343v0.2a38.76 38.76 0 0 0 76.95 6.554c0.065-0.5 0.118-0.995 0.176-1.5a39.86 39.86 0 0 0-2.514-19.47zm-44.67 30.34c0.181 0.087 0.351 0.181 0.537 0.264l0.027 0.017q-0.282-0.135-0.564-0.281zm8.878-23.38m31.95-4.934v-0.037l7e-3 0.041z" style="fill:url(#a)"/>
	 <path d="m74.62 26.83c-1.684-4.052-5.1-8.427-7.775-9.81a40.27 40.27 0 0 1 3.925 11.76v0.037l7e-3 0.041a35.1 35.1 0 0 1-1.206 26.16c-4.442 9.531-15.19 19.3-32.02 18.82-18.18-0.515-34.2-14.01-37.19-31.68-0.545-2.787 0-4.2 0.274-6.465a28.88 28.88 0 0 0-0.623 5.348v0.2a38.76 38.76 0 0 0 76.95 6.554c0.065-0.5 0.118-0.995 0.176-1.5a39.86 39.86 0 0 0-2.514-19.47z" style="fill:url(#b)"/>
	 <path d="m74.62 26.83c-1.684-4.052-5.1-8.427-7.775-9.81a40.27 40.27 0 0 1 3.925 11.76v0.037l7e-3 0.041a35.1 35.1 0 0 1-1.206 26.16c-4.442 9.531-15.19 19.3-32.02 18.82-18.18-0.515-34.2-14.01-37.19-31.68-0.545-2.787 0-4.2 0.274-6.465a28.88 28.88 0 0 0-0.623 5.348v0.2a38.76 38.76 0 0 0 76.95 6.554c0.065-0.5 0.118-0.995 0.176-1.5a39.86 39.86 0 0 0-2.514-19.47z" style="fill:url(#c)"/>
	 <path d="m55.78 31.38c0.084 0.059 0.162 0.118 0.241 0.177a21.1 21.1 0 0 0-3.6-4.695c-12.05-12.05-3.157-26.12-1.658-26.84l0.015-0.022c-9.735 5.7-13.04 16.25-13.34 21.53 0.452-0.031 0.9-0.069 1.362-0.069a19.56 19.56 0 0 1 16.98 9.917z" style="fill:url(#d)"/>
	 <path d="m38.82 33.79c-0.064 0.964-3.47 4.289-4.661 4.289-11.02 0-12.81 6.667-12.81 6.667 0.488 5.614 4.4 10.24 9.129 12.68 0.216 0.112 0.435 0.213 0.654 0.312q0.569 0.252 1.138 0.466a17.24 17.24 0 0 0 5.043 0.973c19.32 0.906 23.06-23.1 9.119-30.07a13.38 13.38 0 0 1 9.345 2.269 19.56 19.56 0 0 0-16.98-9.917c-0.46 0-0.91 0.038-1.362 0.069a19.39 19.39 0 0 0-10.67 4.111c0.591 0.5 1.258 1.168 2.663 2.553 2.63 2.591 9.375 5.275 9.39 5.59z" style="fill:url(#e)"/>
	 <path d="m38.82 33.79c-0.064 0.964-3.47 4.289-4.661 4.289-11.02 0-12.81 6.667-12.81 6.667 0.488 5.614 4.4 10.24 9.129 12.68 0.216 0.112 0.435 0.213 0.654 0.312q0.569 0.252 1.138 0.466a17.24 17.24 0 0 0 5.043 0.973c19.32 0.906 23.06-23.1 9.119-30.07a13.38 13.38 0 0 1 9.345 2.269 19.56 19.56 0 0 0-16.98-9.917c-0.46 0-0.91 0.038-1.362 0.069a19.39 19.39 0 0 0-10.67 4.111c0.591 0.5 1.258 1.168 2.663 2.553 2.63 2.591 9.375 5.275 9.39 5.59z" style="fill:url(#f)"/>
	 <path d="m24.96 24.36c0.314 0.2 0.573 0.374 0.8 0.531a17.97 17.97 0 0 1-0.109-9.473 28.7 28.7 0 0 0-9.329 7.21c0.189-5e-3 5.811-0.106 8.638 1.732z" style="fill:url(#g)"/>
	 <path d="m0.354 42.16c2.991 17.67 19.01 31.17 37.19 31.68 16.83 0.476 27.58-9.294 32.02-18.82a35.1 35.1 0 0 0 1.206-26.16v-0.037c0-0.029-6e-3 -0.046 0-0.037l7e-3 0.065c1.375 8.977-3.191 17.67-10.33 23.56l-0.022 0.05c-13.91 11.33-27.22 6.834-29.91 5q-0.282-0.135-0.564-0.281c-8.109-3.876-11.46-11.26-10.74-17.6a9.953 9.953 0 0 1-9.181-5.775 14.62 14.62 0 0 1 14.25-0.572 19.3 19.3 0 0 0 14.55 0.572c-0.015-0.315-6.76-3-9.39-5.59-1.405-1.385-2.072-2.052-2.663-2.553a11.59 11.59 0 0 0-1-0.758c-0.23-0.157-0.489-0.327-0.8-0.531-2.827-1.838-8.449-1.737-8.635-1.732h-0.018c-1.536-1.947-1.428-8.367-1.34-9.708a6.928 6.928 0 0 0-1.294 0.687 28.22 28.22 0 0 0-3.788 3.245 33.84 33.84 0 0 0-3.638 4.337v6e-3 -7e-3a32.73 32.73 0 0 0-5.2 11.74c-0.019 0.079-1.396 6.099-0.717 9.221z" style="fill:url(#h)"/>
	 <path d="m52.42 26.86a21.1 21.1 0 0 1 3.6 4.7c0.213 0.161 0.412 0.321 0.581 0.476 8.787 8.1 4.183 19.55 3.84 20.36 7.138-5.881 11.7-14.58 10.33-23.56-4.384-10.93-11.82-15.34-17.88-24.93-0.307-0.485-0.614-0.971-0.913-1.484-0.171-0.293-0.308-0.557-0.427-0.8a7.053 7.053 0 0 1-0.578-1.535 0.1 0.1 0 0 0-0.088-0.1 0.138 0.138 0 0 0-0.073 0c-5e-3 0-0.013 9e-3 -0.019 0.011s-0.019 0.011-0.028 0.015c-1.499 0.711-10.39 14.79 1.66 26.83z" style="fill:url(#i)"/>
	 <path d="m56.6 32.04c-0.169-0.155-0.368-0.315-0.581-0.476-0.079-0.059-0.157-0.118-0.241-0.177a13.38 13.38 0 0 0-9.345-2.269c13.94 6.97 10.2 30.97-9.119 30.07a17.24 17.24 0 0 1-5.043-0.973q-0.569-0.213-1.138-0.466c-0.219-0.1-0.438-0.2-0.654-0.312l0.027 0.017c2.694 1.839 16 6.332 29.91-5l0.022-0.05c0.347-0.81 4.951-12.26-3.84-20.36z" style="fill:url(#j)"/>
	 <path d="m21.35 44.74s1.789-6.667 12.81-6.667c1.191 0 4.6-3.325 4.661-4.289a19.3 19.3 0 0 1-14.55-0.572 14.62 14.62 0 0 0-14.25 0.572 9.953 9.953 0 0 0 9.181 5.775c-0.718 6.337 2.632 13.72 10.74 17.6 0.181 0.087 0.351 0.181 0.537 0.264-4.733-2.445-8.641-7.069-9.129-12.68z" style="fill:url(#k)"/>
	 <path d="m74.62 26.83c-1.684-4.052-5.1-8.427-7.775-9.81a40.27 40.27 0 0 1 3.925 11.76l7e-3 0.065c-4.382-10.92-11.81-15.33-17.88-24.92-0.307-0.485-0.614-0.971-0.913-1.484-0.171-0.293-0.308-0.557-0.427-0.8a7.053 7.053 0 0 1-0.578-1.535 0.1 0.1 0 0 0-0.088-0.1 0.138 0.138 0 0 0-0.073 0c-5e-3 0-0.013 9e-3 -0.019 0.011s-0.019 0.011-0.028 0.015l0.015-0.026c-9.735 5.7-13.04 16.25-13.34 21.53 0.452-0.031 0.9-0.069 1.362-0.069a19.56 19.56 0 0 1 16.98 9.917 13.38 13.38 0 0 0-9.345-2.269c13.94 6.97 10.2 30.97-9.119 30.07a17.24 17.24 0 0 1-5.043-0.973q-0.569-0.213-1.138-0.466c-0.219-0.1-0.438-0.2-0.654-0.312l0.027 0.017q-0.282-0.135-0.564-0.281c0.181 0.087 0.351 0.181 0.537 0.264-4.733-2.446-8.641-7.07-9.129-12.68 0 0 1.789-6.667 12.81-6.667 1.191 0 4.6-3.325 4.661-4.289-0.015-0.315-6.76-3-9.39-5.59-1.405-1.385-2.072-2.052-2.663-2.553a11.59 11.59 0 0 0-1-0.758 17.97 17.97 0 0 1-0.109-9.473 28.7 28.7 0 0 0-9.329 7.21h-0.018c-1.536-1.947-1.428-8.367-1.34-9.708a6.928 6.928 0 0 0-1.294 0.687 28.22 28.22 0 0 0-3.788 3.245 33.84 33.84 0 0 0-3.623 4.347v6e-3 -7e-3a32.73 32.73 0 0 0-5.2 11.74l-0.052 0.256c-0.073 0.341-0.4 2.073-0.447 2.445v0a45.09 45.09 0 0 0-0.572 5.403v0.2a38.76 38.76 0 0 0 76.95 6.554c0.065-0.5 0.118-0.995 0.176-1.5a39.86 39.86 0 0 0-2.514-19.47zm-3.845 1.991 7e-3 0.041z" style="fill:url(#l)"/>
	</svg> }
	{type === 'Edge' && <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width={width} height={height} x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><linearGradient id="a" x1="120.572" x2="474.916" y1="351.939" y2="351.939" gradientUnits="userSpaceOnUse"><stop offset="0" style="stop-color:#00376F" stop-color="#00376f"></stop><stop offset="1" style="stop-color:#004E99" stop-color="#004e99"></stop></linearGradient><path d="M314.933 505.421c66.624-15.41 123.285-56.83 158.723-112.986 4.324-6.852-3.301-15.047-10.441-11.216-27.244 14.615-59.578 23.083-94.262 23.083-96.132 0-174.251-65.027-175.862-145.758-.544-27.28 15.737-51.398 39.363-61.48l-.016-.011c-13.967 4.889-29.673 6.91-41.82 15.331-46.071 31.936-74.038 91.028-69.583 144.152 4.324 51.55 35.847 111.958 90.373 138.946 28.515 14.113 65.299 11.816 103.525 9.939z" style="fill:url(#a);" fill=""></path><linearGradient id="b" x1="105.724" x2="402.175" y1="90.282" y2="265.648" gradientUnits="userSpaceOnUse"><stop offset="0" style="stop-color:#00C2DD" stop-color="#00c2dd"></stop><stop offset="1" style="stop-color:#00D34F" stop-color="#00d34f"></stop></linearGradient><path d="M312.618 333.416c17.271 6.302 36.319 9.795 56.335 9.795 78.436 0 142.02-53.612 142.022-119.745C510.975 92.357 388.69 1.185 259.396.012 118.43-1.269 2.319 112.649 1.035 253.615c0 0 35.268-108.92 161.25-108.92 85.246 0 144.847 50.601 154.637 88.852a63.814 63.814 0 0 1 4.06 22.453c0 16.57-6.302 31.668-16.639 43.029-10.242 11.256-6.022 29.17 8.275 34.387z" style="fill:url(#b);" fill=""></path><linearGradient id="c" x1="133.488" x2="213.342" y1="141.756" y2="508.144" gradientUnits="userSpaceOnUse"><stop offset=".002" style="stop-color:#006393" stop-color="#006393"></stop><stop offset=".4" style="stop-color:#0086DE" stop-color="#0086de"></stop><stop offset="1" style="stop-color:#006393" stop-color="#006393"></stop></linearGradient><path d="M316.922 233.547c-9.789-38.251-42.391-111.963-148.293-111.963-162.84 0-167.594 132.031-167.594 132.031C-.262 396.094 114.845 512 257.024 512c19.917 0 39.301-2.276 57.909-6.579-206.514 10.141-227.724-257.532-82.494-308.368l.016.011a62.35 62.35 0 0 1 23.873-5.018c27.721-.295 51.424 17.051 60.594 41.501z" style="fill:url(#c);" fill=""></path></g></svg>}
	{(type !== 'Firefox' && type !== 'Edge') && <svg width={width} height={height} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><linearGradient id="b" x1="55.41" x2="12.11" y1="96.87" y2="21.87" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#1e8e3e"/><stop offset="1" stop-color="#34a853"/></linearGradient><linearGradient id="c" x1="42.7" x2="86" y1="100" y2="25.13" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fcc934"/><stop offset="1" stop-color="#fbbc04"/></linearGradient><linearGradient id="a" x1="6.7" x2="93.29" y1="31.25" y2="31.25" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#d93025"/><stop offset="1" stop-color="#ea4335"/></linearGradient><path fill="url(#a)" d="M93.29 25a50 50 90 0 0-86.6 0l3 54z"/><path fill="url(#b)" d="M28.35 62.5 6.7 25A50 50 90 0 0 50 100l49-50z"/><path fill="url(#c)" d="M71.65 62.5 50 100a50 50 90 0 0 43.29-75H50z"/><path fill="#fff" d="M50 75a25 25 90 1 0 0-50 25 25 90 0 0 0 50z"/><path fill="#1a73e8" d="M50 69.8a19.8 19.8 90 1 0 0-39.6 19.8 19.8 90 0 0 0 39.6z"/></svg>}