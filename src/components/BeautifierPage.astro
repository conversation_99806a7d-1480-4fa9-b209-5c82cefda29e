---
import Beautifier from '@components/Beautifier.jsx';
import Privacy from '@components/Privacy.astro';
import { getLang } from '@i18n/index';
const { locale } = Astro.params;
const l = getLang(locale);
const nav = l.nav;
const t = l.beautifier;
---

<div class="container py-8 relative z-10 flex-1">
    <h1 class="text-4xl text-center leading-[42px] font-bold mb-0.5 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">{nav.beautifier.title}</h1>
    <p class="text-slate-600 text-center mb-2">{t.not}</p>
    <div class="pt-2 pb-4 text-center text-xs [&_a]:inline-block">
        <a href="https://screenshot.shoteasy.fun/" class="py-2 px-6 bg-blue-500/20 border border-blue-300 rounded-full text-blue-600 hover:bg-blue-500/50 hover:text-white">✨<span class="mx-2 underline">{t.new}</span>👉</a>
    </div>
    <div class="pb-12 min-h-[750px]">
        <Beautifier client:only="react" />
    </div>
    <section class="mb-3 rounded-md p-4 bg-sky-100/40 text-xs">
        <div class="mb-1 text-sm">🏙️</div>
        <h2 class="text-lg font-bold mb-2">{t.online}</h2>
        <p>{t.onlineCont}</p>
    </section>
    <section class="mb-3 rounded-md p-4 bg-pink-100/40 text-xs [&>p]:mb-1">
        <div class="mb-1 text-sm">🤩</div>
        <h2 class="text-lg font-bold mb-2">{t.what}</h2>
        <p>{t.whatCont1}</p>
        <p>{t.whatCont2}</p>
        <p>{t.whatCont3}</p>
    </section>
    <Privacy />
</div>
<script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t.title,
    "description": t.description,
    "operatingSystem": "ALL",
    "applicationCategory": "BrowserApplication",
    "browserRequirements": "requires HTML5 support",
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.6",
        "ratingCount": "4872",
        "bestRating": "5"
    },
    "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
    }
})}/>