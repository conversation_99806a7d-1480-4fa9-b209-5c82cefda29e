{"name": "easy-shot-website", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@astrojs/check": "^0.3.4", "@astrojs/react": "^3.0.8", "@astrojs/sitemap": "^3.0.3", "@astrojs/tailwind": "^5.0.4", "@astrojs/vercel": "^6.1.0", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@huggingface/transformers": "^3.0.2", "@tailwindcss/typography": "^0.5.10", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "antd": "^5.12.2", "astro": "^4.0.7", "astro-seo": "^0.8.0", "clsx": "^2.0.0", "colorthief": "^2.4.0", "dayjs": "^1.11.11", "emoji-mart": "^5.5.2", "filesize": "^10.1.2", "highlight.js": "^11.9.0", "html-to-image": "^1.11.11", "js-sha256": "^0.10.1", "jszip": "^3.10.1", "konva": "^9.3.0", "lucide-react": "^0.298.0", "markdown-it": "^14.1.0", "mobx": "^6.12.3", "mobx-react-lite": "^4.0.7", "octokit": "^4.0.2", "react": "^18.0.0", "react-compare-image": "^3.4.0", "react-dom": "^18.0.0", "react-easy-crop": "^5.0.7", "react-filerobot-image-editor": "^4.6.3", "react-konva": "^18.2.10", "svgo": "^3.3.2", "tailwind-merge": "^2.1.0", "tailwindcss": "^3.0.24", "tinykeys": "^2.1.0", "typescript": "^5.2.2"}}